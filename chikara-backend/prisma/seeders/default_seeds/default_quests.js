export const defaultQuests = [
    {
        id: 1,
        name: "Training Day",
        description:
            "Hey there! I'm <PERSON><PERSON><PERSON>, and I'll be helping you get started. First up is your basic stat training. Head over to the training area, pump up some stats, and show us what you've got! Come back here after you complete your first session.",
        questInfo: "You can find the Training area from the Explore page",
        levelReq: 1,
        cashReward: 100,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 2,
        name: "Combat Ready",
        description:
            "Alright, it's time to gear up. Head over to <PERSON><PERSON>'s Shop and buy a pen. Might sound simple, but it's more than just a writing tool around here. Equip it, and you'll see what I mean. It's your first step towards being combat-ready.",
        questInfo:
            "You can find <PERSON><PERSON>'s shop from the Explore page and equip items from the 'Inventory' section at Home",
        levelReq: 1,
        cashReward: 100,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: 1,
    },
    {
        id: 3,
        name: "Dangerous Streets",
        description:
            "Now, let's test your mettle. The streets aren't forgiving, and we've simulated an area just like them. Defeat the end boss in a Streets run to pass the zone and show me you've got what it takes.",
        questInfo: "Head to the Adventure page to start a Streets map.",
        levelReq: 1,
        cashReward: 100,
        xpReward: 1,
        repReward: 0,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: 2,
    },
    {
        id: 4,
        name: "Schoolyard Showdown",
        description:
            "Feeling strong? It's challenge time! Defeat three of the Junior Students at the School. Show them your best moves and wow everyone, including me!",
        questInfo: null,
        levelReq: 2,
        cashReward: 200,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: 3,
    },
    {
        id: 5,
        name: "Pocket Change",
        description:
            "Hey, want to earn some extra cash? It's about time you land your first part-time job! It's a great way to make some money on the side.",
        questInfo: "You can find a Part-Time Job from the Explore page",
        levelReq: 4,
        cashReward: 400,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: 3,
    },
    {
        id: 6,
        name: "Fueling the Flame",
        description:
            "Okay, here's a little mission for you. The church nearby uses these special fuel cans for their lanterns, and we need a couple for our end-of-semester festival. Can you sneak over and grab some for me? It'll be our little secret mission!",
        questInfo: null,
        levelReq: 3,
        cashReward: 400,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: 4,
    },
    {
        id: 7,
        name: "Upskilling",
        description:
            "As you grow, so should your abilities. It's time to master your first ability. You can learn your first combat ability once you've earned enough talent points from leveling up.",
        questInfo: "You can unlock and equip abilities from the 'Talent' and 'Abilities' sections at Home",
        levelReq: 5,
        cashReward: 500,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: 5,
    },
    {
        id: 8,
        name: "Rookie Fighter",
        description:
            "I need you to reach zone 10 in the Streets to prove your mettle. Consider it your final exam for the rookie level. Trust me, it's a great way to show you're ready for more challenging battles!",
        questInfo: null,
        levelReq: 5,
        cashReward: 1000,
        talentPointReward: 1,
        xpReward: 0,
        repReward: 0.1,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 9,
        name: "Craftsmanship 101",
        description:
            "Crafting isn't just a hobby; it's an essential skill for survival. You'll need to learn how to make your own gear sooner or later. Craft your first item at the workshop and bring it back to me.",
        questInfo: "You can craft items in the Workshop from the Campus menu",
        levelReq: 7,
        cashReward: 700,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 10,
        name: "Missing Tech",
        description:
            "We've got a mystery on our hands! Some high-end tech has vanished from the school lab. Could you start snooping around the school for any clues? This equipment is crucial for our final year project, and I'd really appreciate your detective skills here!",
        questInfo: null,
        levelReq: 10,
        cashReward: 1000,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: "The High-Tech Heist Part 1",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 11,
        name: "The Suspicious Figure",
        description:
            "Great work on finding those clues! Based on what you've gathered, it seems like our suspect might be hiding out near the Church. Can you go there and confront them? Be careful, but I'm sure you can handle it. Let's solve this mystery once and for all!",
        questInfo: null,
        levelReq: 10,
        cashReward: 1000,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: "The High-Tech Heist Part 2",
        shopId: 1,
        requiredQuestId: 10,
    },
    {
        id: 12,
        name: "Recovery Operation",
        description:
            "After your brave confrontation, everything points to the sewers. The thief stashed the stolen tech there. There are five crucial pieces we need to recover. I know it's a dirty job, but it's vital for us to get everything back for the academy's sake!",
        questInfo: null,
        levelReq: 10,
        cashReward: 1000,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The High-Tech Heist Part 3",
        shopId: 1,
        requiredQuestId: 11,
    },
    {
        id: 13,
        name: "Intermediate Fighter",
        description:
            "You're doing so well! Now, let's see if you can take it up a notch. I challenge you to reach zone 20 in adventure mode. It's a tough journey, but it's the best way to show you're ready for more advanced battles. I believe in you!",
        questInfo: null,
        levelReq: 10,
        talentPointReward: 1,
        cashReward: 2000,
        xpReward: 0,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: 8,
    },
    {
        id: 14,
        name: "Campus Clash",
        description:
            "Looks like it's time to test your combat skills right here on campus. Why not take on eight of our school's enemies? Show them what you've learned and defend our school's honor. I'll be cheering for you all the way!",
        questInfo: null,
        levelReq: 14,
        cashReward: 1400,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 15,
        name: "Shadows in the Sanctuary",
        description:
            "There's trouble at the church! It's swarming with enemies trying to take over our sanctuary. Can you head there and take down 12 of them? It's crucial to keep that place safe and serene as it always has been!",
        questInfo: null,
        levelReq: 16,
        cashReward: 1600,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 16,
        name: "Precision Strike",
        description:
            "Ready for a challenge that really tests your skills? Try to defeat six opponents while taking as little damage as possible. It's all about precision and control. Show me how elegantly you can win your battles!",
        questInfo: null,
        levelReq: 18,
        cashReward: 1800,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 17,
        name: "Advanced Fighter",
        description:
            "It's time to show what you're really made of! Your next challenge is to reach zone 30 in the Streets. This will prove your skills as an advanced fighter. It's a tough journey, but you've got the spirit and skill to make it.",
        questInfo: null,
        levelReq: 20,
        cashReward: 4000,
        xpReward: 0,
        talentPointReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: 13,
    },
    {
        id: 18,
        name: "Graduation",
        description:
            "You've grown so much from the rookie you once were. To mark your graduation from rookie status, I need you to fetch a graduation cap. Let's make this a celebration to remember and cheer for all your hard work and achievements!",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 19,
        name: "First Blood",
        description:
            "Huh, so you want to prove yourself? Start small then. Defeat your first player and maybe I'll start taking you seriously. Just maybe.",
        questInfo: null,
        levelReq: 5,
        cashReward: 500,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 20,
        name: "Infamy",
        description:
            "Got a taste for the fight, huh? Let's see if you can handle more. Mug two players. Don't disappoint me—it's embarrassing.",
        questInfo: null,
        levelReq: 6,
        cashReward: 600,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 21,
        name: "Rookie Duelist",
        description:
            "You're not as useless as I thought. But don't get cocky. Defeat three players over level 7. Prove your worth, and maybe I'll consider acknowledging your skills.",
        questInfo: null,
        levelReq: 8,
        cashReward: 800,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 22,
        name: "Merciful Warrior",
        description:
            "Winning's one thing, knowing when to stop is another. Win four PvP battles and spare your opponents. Show me you can fight with honor. Impress me, and you just might earn my respect.",
        questInfo: null,
        levelReq: 11,
        cashReward: 1100,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 23,
        name: "Street Brawler",
        description:
            "So, you're getting the hang of this. Now, beat five players over level 10. Show me you can dominate the streets.",
        questInfo: null,
        levelReq: 13,
        cashReward: 1300,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 24,
        name: "Show of Strength",
        description:
            "Think you're strong? Prove it. Take down six players over level 15. Show me your strength isn't just talk.",
        questInfo: null,
        levelReq: 15,
        cashReward: 1500,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 25,
        name: "Champion's Choice",
        description: "You know the drill. Defeat five players and mug them. Let's see how well you can exploit a win.",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 26,
        name: "Worthy Challenge",
        description: "Now for a real test. Defeat five players of at least level 20. Show me you're worth my time.",
        questInfo: null,
        levelReq: 21,
        cashReward: 2100,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Path of the Duelist Part 1",
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 27,
        name: "Rising Through the Ranks",
        description:
            "Your skills are growing. Challenge and defeat seven players over level 22. Show everyone you're climbing the ranks.",
        questInfo: null,
        levelReq: 22,
        cashReward: 2200,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Path of the Duelist Part 2",
        shopId: 2,
        requiredQuestId: 26,
    },
    {
        id: 28,
        name: "Merciless Victor",
        description:
            "Win seven PvP battles against players of level 24 or higher, and make sure they remember you—cripple them. It's harsh, but that's the game.",
        questInfo: null,
        levelReq: 23,
        cashReward: 2300,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: "Path of the Duelist Part 3",
        shopId: 2,
        requiredQuestId: 27,
    },
    {
        id: 29,
        name: "Duelist's Gauntlet",
        description:
            "Time to prove you're among the best. Defeat seven players over level 25. Earn your spot as a top duelist.",
        questInfo: null,
        levelReq: 24,
        cashReward: 2400,
        xpReward: 1,
        repReward: 0.5,
        disabled: false,
        questChainName: "Path of the Duelist Part 4",
        shopId: 2,
        requiredQuestId: 28,
    },
    {
        id: 30,
        name: "Feared By All",
        description:
            "If you want to be feared, you have to earn it. Defeat ten players over level 30. Make them dread hearing your name.",
        questInfo: null,
        levelReq: 25,
        cashReward: 2500,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 31,
        name: "Mercy Amongst Rivals",
        description:
            "Showing mercy can be as powerful as showing strength. Leave eight players over level 28. Show there's honor in your method.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Rising Through the Ranks Part 1",
        shopId: 2,
        requiredQuestId: null,
    },
    {
        id: 32,
        name: "The Path of Vengeance",
        description:
            "Sometimes, fear is necessary. Cripple eight players over level 28. Let them know vengeance is part of your arsenal.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Rising Through the Ranks Part 2",
        shopId: 2,
        requiredQuestId: 31,
    },
    {
        id: 33,
        name: "The Thief's Code",
        description:
            "Mug eight players over level 28. It's dirty work, but it's part of living in the shadows. Show me you can thrive in the darkness.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Rising Through the Ranks Part 3",
        shopId: 2,
        requiredQuestId: 32,
    },
    {
        id: 34,
        name: "Start with the Basics",
        description:
            "You're new around here, right? Let's see if you can handle the basics first. Reach zone 3 in the Streets, then maybe we'll have something to talk about.",
        questInfo: null,
        levelReq: 3,
        cashReward: 300,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 35,
        name: "Mall Crawl",
        description:
            "Okay, you've shown a bit of promise. Now, I need you to gather three lost teacher training materials from the mall. They're pretty crucial for our upcoming defense seminars.",
        questInfo: null,
        levelReq: 7,
        cashReward: 700,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 36,
        name: "Mall Brawl",
        description:
            "There's a bit of a situation here at the mall—some unruly Karens are causing chaos. I'd appreciate it if you could take out four of them. Show them that we mean business here.",
        questInfo: null,
        levelReq: 9,
        cashReward: 900,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 37,
        name: "Price on Power",
        description:
            "If you want to play in the big leagues, you need to know how to use leverage. Place a bounty of at least 500 on another player. It's a powerful move, use it wisely.",
        questInfo: null,
        levelReq: 11,
        cashReward: 0,
        xpReward: 1,
        repReward: 0.1,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 38,
        name: "Atomic Errand",
        description:
            "Here's a tricky one for you. Fetch three nuclear warheads from the shrine. Yes, you heard right. We need them for... a project I'm working on. No questions, please.",
        questInfo: null,
        levelReq: 12,
        cashReward: 1200,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 39,
        name: "Mall Mayhem",
        description:
            "Looks like the mall is under threat again. This time, I need you to step up and take down seven enemies. Prove that you can handle the chaos and you might just earn my respect.",
        questInfo: null,
        levelReq: 14,
        cashReward: 1400,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 40,
        name: "The Iron Fist",
        description:
            "I've heard rumors about a secretive document hidden within the ancient shrine, allegedly containing plans of the mysterious Iron Fist organization. I need you to retrieve it for me. This could be bigger than anything we've dealt with before.",
        questInfo: null,
        levelReq: 16,
        cashReward: 1600,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: "The Iron Fist Conspiracy Part 1",
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 41,
        name: "Iron Fist Patrol",
        description:
            "Great work retrieving the document. It's revealed the presence of Iron Fist corporals patrolling the mall. We need to disrupt their operations. Can you take out four of them? It'll help loosen their grip on the area.",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: "The Iron Fist Conspiracy Part 2",
        shopId: 3,
        requiredQuestId: 40,
    },
    {
        id: 42,
        name: "Covert Operations",
        description:
            "With the corporals down, it's clear they've left behind surveillance gear. I need you to collect this equipment from the mall—it will give us more insight into their next moves. Let's keep the pressure on and dismantle their network piece by piece.",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: "The Iron Fist Conspiracy Part 3",
        shopId: 3,
        requiredQuestId: 41,
    },
    {
        id: 43,
        name: "Monsters Below",
        description:
            "The information from the gear you collected is crucial. It's led us to the Iron Fist hideout, but there's a problem — the sewers are swarming with monsters. Clearing them out is essential for us to access the hideout and put an end to their scheming.",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        talentPointReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The Iron Fist Conspiracy Part 4",
        shopId: 3,
        requiredQuestId: 42,
    },
    {
        id: 44,
        name: "The Iron Fist Champion",
        description:
            "All paths lead here. The champion of the Iron Fist is in the hidden hideout in the Sewers. Taking her down will cripple their operations significantly. This is our chance to strike a decisive blow. Are you ready to end this?",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The Iron Fist Conspiracy Part 5",
        shopId: 3,
        requiredQuestId: 43,
    },
    {
        id: 45,
        name: "The Ordeal",
        description:
            "Here's a challenge for you—take down 6 targets in the Streets in exactly 3 turns. Precision and strategy will be your best tools here. Show me what you've learned.",
        questInfo: null,
        levelReq: 24,
        cashReward: 2400,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 46,
        name: "Shadow Strike",
        description:
            "Demonstrate your stealth and prowess by taking down 5 enemies at the shrine while sustaining less than 15% damage. Move like a shadow, strike unseen.",
        questInfo: null,
        levelReq: 26,
        cashReward: 2600,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 47,
        name: "Shrine of Whispers",
        description:
            "Mysterious whispers at the shrine have led many astray, turning the place into a turmoil zone. Face and defeat any 15 entities causing this chaos. It's time to restore peace to the shrine.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 3,
        requiredQuestId: null,
    },
    {
        id: 48,
        name: "Loafing Around",
        description:
            "Oh, hi there! I just had a brilliant idea—why don't you try baking some bread? Make three loaves and bring them here. It's such a comforting thing to do, don't you think? Just like home!",
        questInfo: null,
        levelReq: 8,
        cashReward: 800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 49,
        name: "Iron Chef",
        description:
            "Now, let's heat things up a bit! Could you craft three iron ingots for me? Think of it as your initiation into the fine art of metalwork—just like we learned in class!",
        questInfo: null,
        levelReq: 9,
        cashReward: 900,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 50,
        name: "Tech Order: Gadgets",
        description:
            "I need your help with a special project. Could you create four special gadgets? They're for a very innovative and hush-hush kind of thing. I'm so excited to see what you come up with!",
        questInfo: null,
        levelReq: 10,
        cashReward: 1000,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 51,
        name: "Modern Remedies",
        description:
            "You've been collecting herbs, right? Fabulous! How about you use them to craft three batches of antibiotics? It's amazing how we can make such essential things from the simplest ingredients!",
        questInfo: null,
        levelReq: 15,
        cashReward: 1500,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 52,
        name: "Circuit Starters",
        description:
            "Next up, let's get technical! Can you craft three basic electrical components for me? It'll be like a mini science fair project. I can't wait to see your handy work!",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 53,
        name: "Feast Preparation",
        description:
            "Oh, it's festival time! Can you help me gather four exotic spices for the feast? They'll add just the right flair to our dishes and make everything taste like magic!",
        questInfo: null,
        levelReq: 18,
        cashReward: 1800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The Festival Feast Part 1",
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 54,
        name: "Master Chef",
        description:
            "You've been doing so wonderfully! Now, I need you to craft the main course for our festival feast. It's a big responsibility, but I know you'll cook up something absolutely delicious!",
        questInfo: null,
        levelReq: 18,
        cashReward: 1800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The Festival Feast Part 2",
        shopId: 4,
        requiredQuestId: 53,
    },
    {
        id: 55,
        name: "Milking them Dry",
        description:
            "For our dessert, we need something extra special—how about some special syrup from those monsters in the sewer? Yes, it's a bit odd, but trust me, it'll be the perfect sweet touch we need!",
        questInfo: null,
        levelReq: 18,
        cashReward: 1800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "The Festival Feast Part 3",
        shopId: 4,
        requiredQuestId: 54,
    },
    {
        id: 56,
        name: "Sweet Finale",
        description:
            "Now for the crown jewel of our feast—a spectacular dessert! Craft something that will dazzle everyone's taste buds and make this festival unforgettable. I can't wait to see what you come up with!",
        questInfo: null,
        levelReq: 18,
        cashReward: 1800,
        xpReward: 1,
        repReward: 0.3,
        talentPointReward: 1,
        disabled: false,
        questChainName: "The Festival Feast Part 4",
        shopId: 4,
        requiredQuestId: 55,
    },
    {
        id: 57,
        name: "Steel the Show",
        description:
            "Let's turn up the heat in the forge! Could you craft four steel ingots for me? They're for a new, exciting project. Your metalwork is really going to steal the show!",
        questInfo: null,
        levelReq: 22,
        cashReward: 2200,
        xpReward: 1,
        repReward: 0.2,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 58,
        name: "Processing Power",
        description:
            "Now, let's get techy! Can you craft two CPUs for me? They're essential for powering up our next big innovation. Your skills are just what we need to keep pushing boundaries!",
        questInfo: null,
        levelReq: 25,
        cashReward: 2500,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 59,
        name: "Crypto Craze",
        description:
            "Here's a fun twist—can you craft a Physical Bitcoin? It's all the rage right now, and I'd love to see your take on this digital dazzler turned physical!",
        questInfo: null,
        levelReq: 27,
        cashReward: 2700,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 60,
        name: "Circuit Wizard",
        description:
            "I'm always amazed by your craftsmanship! Next challenge: craft two advanced electrical components. Let's see if you can really amp up your skills to wizard level!",
        questInfo: null,
        levelReq: 29,
        cashReward: 2900,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: null,
        shopId: 4,
        requiredQuestId: null,
    },
    {
        id: 61,
        name: "Terraformer",
        description:
            "You ever wander into the Arcade? There's this game, 'Terraformer,' you might want to try the demo. It's actually pretty cool, and who knows, you might learn something useful. Or don't—it's up to you, but it could be worth your while.",
        questInfo: null,
        levelReq: 15,
        cashReward: 3000,
        xpReward: 0,
        repReward: 0,
        disabled: false,
        questChainName: null,
        shopId: 5,
        requiredQuestId: null,
    },
    {
        id: 62,
        name: "Sewer Sweep",
        description:
            "So, the sewers are swarming again—monsters everywhere. It's not glamorous, but if you could clean up a bit, say, take out 12 of them, that would be great. Think of it as pest control, except you might actually enjoy it.",
        questInfo: null,
        levelReq: 17,
        cashReward: 1700,
        xpReward: 1,
        repReward: 0.5,
        disabled: false,
        questChainName: null,
        shopId: 5,
        requiredQuestId: null,
    },
    {
        id: 63,
        name: "Alley Ambush",
        description:
            "There's a bit of an infestation in the alley—14 creatures, last I checked. You could help clear them out. It's good practice, and hey, maybe you'll find something interesting among the leftovers.",
        questInfo: null,
        levelReq: 22,
        cashReward: 2200,
        xpReward: 1,
        repReward: 0.5,
        disabled: false,
        questChainName: null,
        shopId: 5,
        requiredQuestId: null,
    },
    {
        id: 64,
        name: "Ring Fling",
        description:
            "I recently recieved some intel on something super exciting: the legendary Ring of Fate! I need you to find the mould for this ring - it was last seen in the Alley. This isn't just another trinket, it's steeped in history and power.",
        questInfo: null,
        levelReq: 27,
        cashReward: 2700,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Ring Part 1",
        shopId: 5,
        requiredQuestId: null,
    },
    {
        id: 65,
        name: "Gem Shards Galore",
        description:
            "Fantastic job finding that mould! Next up, we need to collect the 6 broken gem shards from the original Ring. Unfortunately we've discovered that they're deep down in the Sewers. It's not exactly a walk in the park, but I have a hunch you'll get it done.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Ring Part 2",
        shopId: 5,
        requiredQuestId: 64,
    },
    {
        id: 66,
        name: "Ring It Together",
        description:
            "You really pulled it off, didn't you? With all the missing pieces you've found, we’re all set to craft the legendary Ring of Fate! Go ahead, carefully put everything together. And you know what? Keep the ring. It's a small token for your huge effort.",
        questInfo: null,
        levelReq: 28,
        cashReward: 2800,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: "Legendary Ring Part 3",
        shopId: 5,
        requiredQuestId: 65,
    },
    {
        id: 67,
        name: "Echoes of the Past",
        description:
            "I've learned of some mysterious blueprints located deep within the old Shrine. They outline the design of a legendary weapon. Sounds like something out of a story, right? Retrieve them and let's bring this legend to life!",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Weapon Part 1",
        shopId: 5,
        requiredQuestId: null,
    },
    {
        id: 68,
        name: "Starforged Iron",
        description:
            "The Ancient Blueprints you retrieved are clear: we need Starforged Iron, a metal as old as the stars themselves. It's found deep within the city's Sewers. Gather eight pieces of this rare iron - it's vital for our weapon's strength!",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Weapon Part 2",
        shopId: 5,
        requiredQuestId: 67,
    },
    {
        id: 69,
        name: "Guardian of Flames",
        description:
            "According to our blueprints, the next ingredient is no ordinary flame — it's guarded by the Guardian of Flames, located underneath the mall. Defeat this guardian to infuse our legendary weapon with unmatched strength.",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Weapon Part 3",
        shopId: 5,
        requiredQuestId: 68,
    },
    {
        id: 70,
        name: "Crystallized Moonlight",
        description:
            "The blueprints describe Crystallized Moonlight as essential for enchanting the weapon. You’ll find these glowing shards around the school rooftops. They're not just for show; their mystical properties will imbue our weapon with power unseen in centuries!",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.3,
        disabled: false,
        questChainName: "Legendary Weapon Part 4",
        shopId: 5,
        requiredQuestId: 69,
    },
    {
        id: 71,
        name: "Whispering Winds",
        description:
            "Next, the blueprints call for capturing the Whispering Winds from the top of the church tower. These aren't just breezes—they're the breath of the ancients, needed to temper our weapon to perfection. It's as poetic as it is pivotal.",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: "Legendary Weapon Part 5",
        shopId: 5,
        requiredQuestId: 70,
    },
    {
        id: 72,
        name: "Harvester of Spirits",
        description:
            "Now, here's the unsettling part—the blueprints demand the lifeforce of formidable foes. You'll need to cripple 5 worthy opponents in combat. It's grim, but this lifeforce is crucial for the final tempering of our legendary weapon.",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.4,
        disabled: false,
        questChainName: "Legendary Weapon Part 6",
        shopId: 5,
        requiredQuestId: 71,
    },
    {
        id: 73,
        name: "Forging Destiny",
        description:
            "All the elements dictated by the blueprints are now within our grasp. It's time to craft the base version of the legendary weapon—a sword or a gun, your choice. This isn't just assembling parts; it's awakening history, melding myth into metal.",
        questInfo: null,
        levelReq: 30,
        cashReward: 3000,
        xpReward: 1,
        repReward: 0.5,
        disabled: false,
        questChainName: "Legendary Weapon Part 7",
        shopId: 5,
        requiredQuestId: 72,
    },
    {
        id: 74,
        name: "Suggestions Board",
        description:
            "Thanks for playing Chikara Academy! If you have any suggestions for game features or improvements then please leave them on the Suggestions board found at the School page.",
        questInfo: "You can find the Suggestions Board from the Campus page",
        levelReq: 8,
        cashReward: 0,
        xpReward: 0,
        repReward: 0,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 75,
        name: "Mugshot Makeover",
        description:
            "Every hero has a unique face. Set yourself an avatar to make yourself known and easily recognizable throughout the academy! What will your signature look be?",
        questInfo: "You can upload an Avatar from the Settings page",
        levelReq: 3,
        cashReward: 300,
        xpReward: 1,
        repReward: 0,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 76,
        name: "Helping the Community",
        description:
            "Ready to make a splash in our community? I've got a sweet little task for you. Just head over to the mission board and complete any mission listed there. Easy peasy, right? ",
        questInfo: "You can access the mission board from the Campus page",
        levelReq: 4,
        cashReward: 400,
        xpReward: 1,
        repReward: 0,
        disabled: false,
        questChainName: "Tutorial",
        shopId: 1,
        requiredQuestId: null,
    },
    {
        id: 77,
        name: "Elixir of Shadows",
        description:
            "Hey there, you don't look so good... like, not at all. I've heard rumors of an ancient recipe that can reverse... whatever's happening to you. If you bring me three of those rare Nightshade plants I'll show you how to make the Cure.",
        questInfo: null,
        levelReq: 30,
        cashReward: 15000,
        xpReward: 0,
        repReward: 0,
        disabled: false,
        questChainName: null,
        shopId: 1,
        requiredQuestId: null,
    },
];

export default defaultQuests;
