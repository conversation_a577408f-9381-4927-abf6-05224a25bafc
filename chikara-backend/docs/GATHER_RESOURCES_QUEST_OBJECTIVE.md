# GATHER_RESOURCES Quest Objective Type

## Overview

The `GATHER_RESOURCES` quest objective type allows quest creators to set objectives that require players to gather resources through mining, scavenging, or foraging activities.

## How It Works

When players perform resource gathering activities (mining, scavenging, or foraging), the system automatically emits `RESOURCE_GATHERED` events that are processed by the quest system to update relevant quest objectives.

## Objective Configuration

### Database Fields

- **objectiveType**: `GATHER_RESOURCES`
- **target**: Item ID (specific item) or `null` (any item)
- **targetAction**: Activity type (`"mining"`, `"scavenging"`, `"foraging"`) or `null` (any activity)
- **quantity**: Number of resources to gather
- **location**: Optional location restriction

### Examples

#### 1. Gather specific item through any activity

```sql
INSERT INTO quest_objective (
    questId, objectiveType, description, quantity, target, targetAction
) VALUES (
    1, 'GATHER_RESOURCES', 'Gather 10 Iron Ore', 10, 123, NULL
);
```

#### 2. Gather any item through mining

```sql
INSERT INTO quest_objective (
    questId, objectiveType, description, quantity, target, targetAction
) VALUES (
    2, 'GATHER_RESOURCES', 'Mine 5 resources', 5, NULL, 'mining'
);
```

#### 3. Gather specific item through specific activity

```sql
INSERT INTO quest_objective (
    questId, objectiveType, description, quantity, target, targetAction
) VALUES (
    3, 'GATHER_RESOURCES', 'Scavenge 3 Scrap Metal', 3, 456, 'scavenging'
);
```

#### 4. Gather any item through foraging

```sql
INSERT INTO quest_objective (
    questId, objectiveType, description, quantity, target, targetAction
) VALUES (
    4, 'GATHER_RESOURCES', 'Forage 7 plants', 7, NULL, 'foraging'
);
```

## Technical Implementation

### Event Flow

1. Player performs resource gathering activity (mining/scavenging/foraging)
2. System adds item to player's inventory
3. System emits `RESOURCE_GATHERED` event with:
    - `userId`: Player ID
    - `itemId`: Item that was gathered
    - `quantity`: Amount gathered
    - `activityType`: Type of activity ("mining", "scavenging", "foraging")
    - `location`: Optional location where gathering occurred

4. Event handler processes the event and calls `handleResourceGathering()`
5. Quest system checks for matching objectives and updates progress

**Note**: Achievement tracking for resource gathering activities is not currently implemented in the user_achievements table, but can be added in the future if needed.

### Matching Logic

The system checks for objectives in this order:

1. **Specific item + specific activity**: `target = itemId AND targetAction = activityType`
2. **Any item + specific activity**: `target = NULL AND targetAction = activityType`
3. **Specific item + any activity**: `target = itemId AND targetAction = NULL`

This allows for flexible quest design where objectives can be:

- Very specific (gather X of item Y through activity Z)
- Activity-focused (gather X items through activity Y)
- Item-focused (gather X of item Y through any activity)
- General (gather X items through any activity - though this should use `ACQUIRE_ITEM` instead)

## Integration Points

### Files Modified

- `chikara-backend/src/types/quest.ts` - Added `GATHER_RESOURCES` type
- `chikara-frontend/src/types/quest.ts` - Added `GATHER_RESOURCES` type
- `chikara-backend/prisma/schema.prisma` - Added enum value
- `chikara-backend/src/core/events/event-types.ts` - Added `RESOURCE_GATHERED` event
- `chikara-backend/src/core/quest.service.ts` - Added `handleResourceGathering()` function
- `chikara-backend/src/core/events/handlers/item-event-handlers.ts` - Added event handler
- Resource gathering controllers - Added event emission

### Activities That Trigger Events

- **Explore Mining**: When players successfully mine ore in explore nodes
- **Explore Scavenging**: When players successfully scavenge items in explore nodes
- **Explore Foraging**: When players successfully forage plants in explore nodes
- **Skills Mining**: When players complete mining sessions and receive ore
- **Skills Scavenging**: When players complete scavenging sessions and receive items

## Usage Recommendations

1. **Use specific combinations** for tutorial or story quests
2. **Use activity-specific objectives** for skill-focused quests
3. **Use item-specific objectives** when you want players to gather particular resources
4. **Consider location restrictions** for area-specific quests
5. **Set reasonable quantities** based on activity difficulty and item rarity

## Testing

Tests are included in:

- `chikara-backend/src/core/__tests__/quest.service.test.ts`
- `chikara-backend/src/core/events/handlers/__tests__/resource-gathering-event.test.ts`

Run tests with:

```bash
npm test -- src/core/__tests__/quest.service.test.ts
npm test -- src/core/events/handlers/__tests__/resource-gathering-event.test.ts
```
