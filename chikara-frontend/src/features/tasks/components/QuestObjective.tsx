import { getUserItemCount } from "@/helpers/getUserItemCount";
import { useGetInventory } from "@/hooks/api/useGetInventory";
import { getObjectiveText } from "@/hooks/useGetQuestObjectiveText";
import { cn } from "@/lib/utils";
import { QuestObjectiveTypes, type QuestObjectiveWithProgress } from "@/types/quest";
import { useHandInItem } from "../api/useHandInItem";
import { getObjectiveProgress } from "../helpers/getObjectiveProgress";

export default function QuestObjective({
    objective,
    status,
}: {
    objective: QuestObjectiveWithProgress;
    status: "available" | "in_progress" | "complete" | "ready_to_complete";
}) {
    const { mutate: handInItem, isPending: isHandingIn } = useHandInItem();
    const { data: inventory } = useGetInventory();

    // Helper function to check if user has enough items for hand-in
    const hasEnoughItemsForHandIn = (obj: QuestObjectiveWithProgress): boolean => {
        if (!inventory || !obj.item || !obj.itemId) return false;

        const itemCount = getUserItemCount(inventory, obj.itemId);
        return itemCount >= (obj.quantity || 1);
    };

    // Handler for item hand-in
    const handleHandInItem = (obj: QuestObjectiveWithProgress) => {
        console.log(obj);
        if (obj.itemId) {
            handInItem({
                objectiveId: obj.id,
                itemId: obj.itemId,
            });
        }
    };

    const objectiveProgress =
        status === "complete" ? objective.quantity : getObjectiveProgress(objective, inventory || []);
    const individualProgressPercentage = objective.quantity > 0 ? (objectiveProgress / objective.quantity) * 100 : 0;
    const isItemHandIn = objective.objectiveType === QuestObjectiveTypes.DELIVER_ITEM;
    const canHandIn =
        isItemHandIn &&
        status === "in_progress" &&
        hasEnoughItemsForHandIn(objective) &&
        objectiveProgress < (objective.quantity || 0);

    return (
        <div key={objective.id} className="space-y-1 gap-6 flex items-center">
            <div className="flex-1">
                <div className="flex justify-between text-xs">
                    <span className="text-gray-100">{getObjectiveText(objective)}</span>
                    {status !== "available" && (
                        <span className="text-gray-300">
                            <span className="font-accent">{objectiveProgress}</span>/
                            <span className="font-accent">{objective.quantity}</span>
                        </span>
                    )}
                </div>
                {status !== "available" && (
                    <div className="w-full h-2 mt-0.5 bg-gray-800 rounded-xs overflow-hidden">
                        <div
                            style={{ width: `${individualProgressPercentage}%` }}
                            className={cn(
                                "h-full rounded-xs",
                                status === "in_progress"
                                    ? "bg-blue-600"
                                    : individualProgressPercentage >= 100
                                      ? "bg-green-600"
                                      : "bg-blue-600"
                            )}
                        ></div>
                    </div>
                )}
            </div>
            {/* Hand In Button for DELIVER_ITEM objectives */}
            {objective.objectiveType === QuestObjectiveTypes.DELIVER_ITEM && (
                <button
                    disabled={!canHandIn || isHandingIn}
                    className="px-3 mr-2 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={() => handleHandInItem(objective)}
                >
                    {isHandingIn ? "Handing In..." : "Hand In Items"}
                </button>
            )}
        </div>
    );
}
