import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

/**
 * Hook for purchasing a property
 */
export const usePurchaseProperty = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.property.purchaseProperty.mutationOptions({
            onSuccess: () => {
                toast.success("Property purchased successfully!");
                // Invalidate relevant queries
                queryClient.invalidateQueries({
                    queryKey: api.property.getHousingList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.property.getUserProperties.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "Failed to purchase property");
            },
        })
    );
};

/**
 * Hook for selling a property
 */
export const useSellProperty = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.property.sellProperty.mutationOptions({
            onSuccess: (data) => {
                toast.success(`Property sold for ¥${data.soldFor.toLocaleString()}!`);
                // Invalidate relevant queries
                queryClient.invalidateQueries({
                    queryKey: api.property.getHousingList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.property.getUserProperties.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "Failed to sell property");
            },
        })
    );
};

/**
 * Hook for setting a property as primary
 */
export const useSetPrimaryProperty = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.property.setPrimaryProperty.mutationOptions({
            onSuccess: () => {
                toast.success("Primary property updated!");
                // Invalidate relevant queries
                queryClient.invalidateQueries({
                    queryKey: api.property.getHousingList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.property.getUserProperties.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "Failed to set primary property");
            },
        })
    );
};
