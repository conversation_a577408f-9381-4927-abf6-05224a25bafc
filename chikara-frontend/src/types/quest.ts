/* eslint-disable no-unused-vars */
import { Item } from "./item";
import type { AppRouterClient } from "@/lib/orpc";

export const QuestObjectiveTypes = {
    DEFEAT_NPC: "DEFEAT_NPC",
    DEFEAT_NPC_IN_TURNS: "DEFEAT_NPC_IN_TURNS",
    DEFEAT_NPC_WITH_LOW_DAMAGE: "DEFEAT_NPC_WITH_LOW_DAMAGE",
    DEFEAT_PLAYER: "DEFEAT_PLAYER",
    PVP_POST_BATTLE_CHOICE: "PVP_POST_BATTLE_CHOICE",
    ACQUIRE_ITEM: "ACQUIRE_ITEM",
    CRAFT_ITEM: "CRAFT_ITEM",
    DELIVER_ITEM: "DELIVER_ITEM",
    PLACE_BOUNTY: "PLACE_BOUNTY",
    UNIQUE_OBJECTIVE: "UNIQUE_OBJECTIVE",
    COMPLETE_MISSIONS: "COMPLETE_MISSIONS",
    DONATE_TO_SHRINE: "DONATE_TO_SHRINE",
    DEFEAT_BOSS: "DEFEAT_BOSS",
    VOTE_ON_SUGGESTION: "VOTE_ON_SUGGESTION",
    CHARACTER_ENCOUNTERS: "CHARACTER_ENCOUNTERS",
    WIN_BATTLE: "WIN_BATTLE",
    COLLECT_BOUNTY_REWARD: "COLLECT_BOUNTY_REWARD",
    TRAIN_STATS: "TRAIN_STATS",
    GAMBLING_SLOTS: "GAMBLING_SLOTS",
    DEFEAT_PLAYER_XNAME: "DEFEAT_PLAYER_XNAME",
    DEFEAT_SPECIFIC_PLAYER: "DEFEAT_SPECIFIC_PLAYER",
    // Resource gathering objectives
    GATHER_RESOURCES: "GATHER_RESOURCES",
    // Story quest objectives
    COMPLETE_STORY_EPISODE: "COMPLETE_STORY_EPISODE",
    // MAKE_STORY_CHOICE: "MAKE_STORY_CHOICE",
    // REACH_RELATIONSHIP_LEVEL: "REACH_RELATIONSHIP_LEVEL",
} as const;
export type QuestObjectiveTypes = (typeof QuestObjectiveTypes)[keyof typeof QuestObjectiveTypes];

export enum QuestProgressStatus {
    complete = "complete",
    in_progress = "in_progress",
    ready_to_complete = "ready_to_complete",
}

export type QuestReward = {
    id: number;
    rewardType: string;
    quantity: number;
    isChoice: boolean;
    questId?: number | null;
    itemId?: number | null;
    item?: Item | null;
};

export type QuestWithProgress = Awaited<ReturnType<AppRouterClient["quest"]["getAvailable"]>>[number];
export type QuestObjectiveWithProgress = QuestWithProgress["quest_objective"][number];

export type DailyQuest = {
    id: number;
    questStatus: QuestProgressStatus;
    objectiveType: QuestObjectiveTypes;
    target: number | null;
    targetAction: string | null;
    quantity: number | null;
    location: string | null;
    count: number;
    userId: number;
    cashReward: number;
    xpReward: number;
    itemRewardId: number | null;
    itemRewardQuantity: number | null;
    item: Item | null;
};

export interface QuestGiver {
    id: number;
    name: string;
    shopType: string;
    avatar: string | null;
    description: string;
    disabled?: boolean | null;
}
