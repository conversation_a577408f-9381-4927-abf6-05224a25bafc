import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { DailyQuest, QuestObjectiveTypes, QuestObjectiveWithProgress } from "@/types/quest";
import React, { useMemo } from "react";

/**
 * Map of quest objective types to their text templates
 */
export const objectiveText: Record<QuestObjectiveTypes, string> = {
    [QuestObjectiveTypes.TRAIN_STATS]: "Train {quantity} Stat Points",
    [QuestObjectiveTypes.GAMBLING_SLOTS]: "Gamble ¥{quantity}",
    [QuestObjectiveTypes.DEFEAT_SPECIFIC_PLAYER]: "Defeat player #{target} {quantity} Time{pluralSuffix}",
    [QuestObjectiveTypes.DEFEAT_NPC]: "Defeat {quantity} NPC{pluralSuffix}",
    [QuestObjectiveTypes.PVP_POST_BATTLE_CHOICE]: "'{targetAction}' {quantity} player{pluralSuffix} in PvP",
    [QuestObjectiveTypes.COMPLETE_MISSIONS]: "Complete {quantity} Mission{pluralSuffix}",
    [QuestObjectiveTypes.DEFEAT_NPC_WITH_LOW_DAMAGE]: "Defeat NPCs while taking <{target}% Damage",
    [QuestObjectiveTypes.DEFEAT_PLAYER_XNAME]: "Beat players who have an '{targetAction}' in their name",
    [QuestObjectiveTypes.VOTE_ON_SUGGESTION]: "Vote on {quantity} Suggestion{pluralSuffix}",
    [QuestObjectiveTypes.CHARACTER_ENCOUNTERS]: "Complete {quantity} Character encounter{pluralSuffix}",
    [QuestObjectiveTypes.DONATE_TO_SHRINE]: "Donate to the Shrine",
    [QuestObjectiveTypes.DEFEAT_NPC_IN_TURNS]: "Defeat {quantity} NPC{pluralSuffix} in exactly {target} Turns",
    [QuestObjectiveTypes.DEFEAT_BOSS]: "Defeat {quantity} NPC Bosses",
    // [QuestObjectiveTypes.USE_ABILITY]: "Use combat abilities {quantity} Time{pluralSuffix}",
    [QuestObjectiveTypes.WIN_BATTLE]: "Win {quantity} Battle{pluralSuffix}",
    [QuestObjectiveTypes.DEFEAT_PLAYER]: "Win {quantity} PvP Battle{pluralSuffix}",
    [QuestObjectiveTypes.COLLECT_BOUNTY_REWARD]: "Claim {quantity} Bounty{pluralSuffix}",
    [QuestObjectiveTypes.ACQUIRE_ITEM]: "Find {quantity} {targetItemName} in the {location}",
    [QuestObjectiveTypes.CRAFT_ITEM]: "Craft {quantity} {targetItemName}",
    [QuestObjectiveTypes.DELIVER_ITEM]: "Hand in {quantity} {targetItemName}",
    [QuestObjectiveTypes.PLACE_BOUNTY]: "Place a bounty worth at least ¥{target}",
    [QuestObjectiveTypes.UNIQUE_OBJECTIVE]: "",
    // Story quest objectives
    // [QuestObjectiveTypes.MAKE_STORY_CHOICE]: "Make the '{targetAction}' choice",
    [QuestObjectiveTypes.COMPLETE_STORY_EPISODE]: "Complete the story episode at {location}",
} as Record<QuestObjectiveTypes, string>;

/**
 * Formats a quest objective text by replacing template variables with actual values
 * @param quest - The object containing quest objective details
 * @returns A formatted string with quest details
 */
export const getObjectiveText = (quest: QuestObjectiveWithProgress | DailyQuest): string => {
    let text = objectiveText[quest.objectiveType] || quest.objectiveType || "Complete the objective";

    if (text.includes("{quantity}")) {
        text = text.replace(
            "{quantity}",
            quest.quantity === null || quest.quantity === undefined ? "-" : quest.quantity.toString()
        );
    }

    if (text.includes("{location}")) {
        text = text.replace(
            "{location}",
            quest.location === null || quest.location === undefined ? "-" : capitaliseFirstLetter(quest.location) || "-"
        );
    }

    if (text.includes("{target}")) {
        text = text.replace(
            "{target}",
            quest.target === null || quest.target === undefined ? "-" : quest.target.toString()
        );
    }

    if (text.includes("{targetAction}")) {
        text = text.replace(
            "{targetAction}",
            quest.targetAction === null || quest.targetAction === undefined
                ? "-"
                : capitaliseFirstLetter(quest.targetAction) || "-"
        );
    }

    if (text.includes("{targetItemName}")) {
        // For CRAFT_ITEM objectives, if no specific item is set, it means "any item"
        if (quest.objectiveType === QuestObjectiveTypes.CRAFT_ITEM && (!quest.item || quest.item === null)) {
            const quantity = quest.quantity === null || quest.quantity === undefined ? 0 : quest.quantity;
            const pluralSuffix = quantity === 1 ? "" : "s";
            text = text.replace("{targetItemName}", `Item${pluralSuffix}`);
        } else {
            text = text.replace(
                "{targetItemName}",
                quest.item === null || quest.item === undefined ? "-" : quest.item.name
            );
        }
    }

    if (text.includes("{pluralSuffix}")) {
        const quantity = quest.quantity === null || quest.quantity === undefined ? 0 : quest.quantity;
        text = text.replace("{pluralSuffix}", quantity === 1 ? "" : "s");
    }

    return text;
};

/**
 * Hook that returns a span element containing formatted quest objective text
 * @param quest - The quest objective details
 * @param className - Optional CSS class to apply to the span
 * @returns A React span element with the formatted quest objective text
 */
const useQuestObjectiveText = (
    quest: QuestObjectiveWithProgress | DailyQuest,
    className?: string
): React.ReactElement => {
    const text = useMemo(() => getObjectiveText(quest), [quest]);

    return <span className={className}>{text}</span>;
};

export default useQuestObjectiveText;

// import reactStringReplace from "react-string-replace";

// const getQuestText = (quest) => {
//     let text = dailyQuestText[quest.objectiveType];
//     text = reactStringReplace(text, "{quantity}", (match, i) => <span key={`quantity-${i}`}>{quest.quantity}</span>);
//     text = reactStringReplace(text, "{location}", (match, i) => <span key={`location-${i}`}>{quest.location}</span>);
//     text = reactStringReplace(text, "{target}", (match, i) => <span key={`target-${i}`}>{quest.target}</span>);
//     text = reactStringReplace(text, "{targetAction}", (match, i) => (
//         <span key={`targetAction-${i}`}>{capitaliseFirstLetter(quest.targetAction)}</span>
//     ));
//     return text;
// };
