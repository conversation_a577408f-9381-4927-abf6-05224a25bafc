// SHOPS
const shopTypes = [
    { id: 1, name: "Weapon" },
    { id: 2, name: "<PERSON><PERSON>" },
    { id: 3, name: "General" },
    { id: 4, name: "Food" },
    { id: 5, name: "Furniture" },
];

const shopGlobals = {
    shopTypes,
};

// ITEMS
const itemTypes = [
    { id: 1, name: "Weapon" },
    { id: 2, name: "Head" },
    { id: 3, name: "Chest" },
    { id: 4, name: "Hands" },
    { id: 5, name: "Legs" },
    { id: 6, name: "Feet" },
    { id: 7, name: "Finger" },
    { id: 8, name: "Offhand" },
    { id: 9, name: "Consumable" },
    { id: 10, name: "Material" },
    { id: 11, name: "Junk" },
    { id: 11, name: "Special" },
];

const itemRarity = [
    { id: 1, name: "Novice" },
    { id: 2, name: "Standard" },
    { id: 3, name: "Enhanced" },
    { id: 4, name: "Specialist" },
    { id: 5, name: "Military" },
    { id: 6, name: "Legendary" },
];

const itemGlobals = {
    itemTypes,
    itemRarity,
};

const questTypes = {
    // Quests where count >= quantity to complete
    kill_quests: [
        "DEFEAT_NPC",
        "DEFEAT_PLAYER",
        "PVP_POST_BATTLE_CHOICE",
        "DEFEAT_NPC_WITH_LOW_DAMAGE",
        "DEFEAT_NPC_IN_TURNS",
        "PLACE_BOUNTY",
    ],

    // Quests where inventory item count >= quantity to complete
    item_quests: ["ACQUIRE_ITEM", "CRAFT_ITEM", "GATHER_RESOURCES"],
};

const questGlobals = {
    questTypes,
};

export { shopGlobals, itemGlobals, questGlobals };
